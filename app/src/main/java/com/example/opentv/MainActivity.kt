package com.example.opentv

import android.os.Bundle
import android.os.PersistableBundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import com.example.opentv.repository.PlaylistRepository
import com.example.opentv.ui.screens.PlaylistScreen
import com.example.opentv.ui.screens.PlayerScreen
import com.example.opentv.ui.theme.OpenTVTheme

class MainActivity : ComponentActivity() {
    private lateinit var playlistRepository: PlaylistRepository

    // onCreate 在页面首次创建的时候执行
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 启用边缘到边缘
        enableEdgeToEdge()

        // 初始化播放列表仓库
        playlistRepository = PlaylistRepository()

        // 加载示例播放列表
        loadSamplePlaylist()

        setContent {
            OpenTVTheme {
                val isTv = LocalConfiguration.current.uiMode and
                    android.content.res.Configuration.UI_MODE_TYPE_MASK ==
                    android.content.res.Configuration.UI_MODE_TYPE_TELEVISION

                // 当这个类型的变量状态改变的时候，界面会自动刷新
                var showPlayer by remember { mutableStateOf(false) }

                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    // 如果是播放界面
                    if (showPlayer) {
                        PlayerScreen(
                            playlistRepository = playlistRepository,
                            onExit = { showPlayer = false }
                        )
                    } else {
                        // 播放列表界面
                        PlaylistScreen(
                            playlistRepository = playlistRepository,
                            onPlayChannel = { showPlayer = true },
                            onExit = { finish() } // 退出应用
                        )
                    }
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?, persistentState: PersistableBundle?) {
        super.onCreate(savedInstanceState, persistentState)
    }

    private fun loadSamplePlaylist() {
        val sampleM3U = """
            #EXTM3U
            #EXTINF:-1 tvg-id="channel1" tvg-name="Channel 1" tvg-logo="" group-title="新闻",Channel 1
            http://example.com/channel1.m3u8
            #EXTINF:-1 tvg-id="channel2" tvg-name="Channel 2" tvg-logo="" group-title="娱乐",Channel 2
            http://example.com/channel2.m3u8
            #EXTINF:-1 tvg-id="channel3" tvg-name="Channel 3" tvg-logo="" group-title="体育",Channel 3
            http://example.com/channel3.m3u8
            #EXTINF:-1 tvg-id="channel4" tvg-name="Channel 4" tvg-logo="" group-title="电影",Channel 4
            http://example.com/channel4.m3u8
        """.trimIndent()

        playlistRepository.loadPlaylist(sampleM3U)
    }
}

