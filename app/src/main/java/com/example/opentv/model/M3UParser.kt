package com.example.opentv.model

class M3UParser {
    companion object {
        fun parse(content: String): List<PlaylistItem> {
            val items = mutableListOf<PlaylistItem>()
            val lines = content.lines()
            
            var currentName = ""
            var currentLogo: String? = null
            var currentGroup: String? = null
            var currentTvgId: String? = null
            var currentTvgName: String? = null
            
            for (line in lines) {
                if (line.startsWith("#EXTINF:")) {
                    // 解析扩展信息行
                    val info = line.substringAfter("#EXTINF:")
                    
                    // 提取名称
                    currentName = info.substringAfter(",", "").trim()
                    
                    // 提取logo
                    currentLogo = extractAttribute(info, "tvg-logo")
                    
                    // 提取分组
                    currentGroup = extractAttribute(info, "group-title")
                    
                    // 提取tvg-id
                    currentTvgId = extractAttribute(info, "tvg-id")
                    
                    // 提取tvg-name
                    currentTvgName = extractAttribute(info, "tvg-name")
                } else if (line.startsWith("http") && currentName.isNotEmpty()) {
                    // 解析URL行
                    items.add(
                        PlaylistItem(
                            name = currentName,
                            url = line.trim(),
                            logo = currentLogo,
                            group = currentGroup,
                            tvgId = currentTvgId,
                            tvgName = currentTvgName
                        )
                    )
                    
                    // 重置当前项
                    currentName = ""
                    currentLogo = null
                    currentGroup = null
                    currentTvgId = null
                    currentTvgName = null
                }
            }
            
            return items
        }
        
        private fun extractAttribute(line: String, attribute: String): String? {
            val regex = Regex("$attribute=\"(.*?)\"")
            val match = regex.find(line)
            return match?.groupValues?.get(1)
        }
    }
}