package com.example.opentv.repository

import com.example.opentv.model.M3UParser
import com.example.opentv.model.PlaylistItem
import java.io.InputStream
import java.io.InputStreamReader

class PlaylistRepository {
    private var playlist: List<PlaylistItem> = emptyList()
    private var currentIndex: Int = 0
    
    fun loadPlaylist(m3uContent: String) {
        playlist = M3UParser.parse(m3uContent)
        currentIndex = 0
    }
    
    fun loadPlaylist(inputStream: InputStream) {
        val content = inputStream.bufferedReader().use { it.readText() }
        loadPlaylist(content)
    }
    
    fun getChannels(): List<PlaylistItem> = playlist
    
    fun getCurrentChannel(): PlaylistItem? = playlist.getOrNull(currentIndex)
    
    fun getNextChannel(): PlaylistItem? {
        currentIndex = (currentIndex + 1) % playlist.size
        return getCurrentChannel()
    }
    
    fun getPreviousChannel(): PlaylistItem? {
        currentIndex = if (currentIndex - 1 < 0) playlist.size - 1 else currentIndex - 1
        return getCurrentChannel()
    }
    
    fun jumpToChannel(index: Int): PlaylistItem? {
        if (index in playlist.indices) {
            currentIndex = index
            return getCurrentChannel()
        }
        return null
    }
    
    fun getChannelCount(): Int = playlist.size
    
    fun getCurrentIndex(): Int = currentIndex
}