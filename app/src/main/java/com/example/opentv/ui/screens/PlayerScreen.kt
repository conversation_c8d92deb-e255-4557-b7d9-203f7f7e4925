package com.example.opentv.ui.screens

import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.foundation.background
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.KeyEventType
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.onPreviewKeyEvent
import androidx.compose.ui.input.key.type
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.example.opentv.model.PlaylistItem
import com.example.opentv.repository.PlaylistRepository
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.ui.StyledPlayerView

@Composable
fun PlayerScreen(
    playlistRepository: PlaylistRepository,
    onExit: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val focusRequester = remember { FocusRequester() }
    var currentChannel by remember { mutableStateOf<PlaylistItem?>(null) }
    var isFocused by remember { mutableStateOf(false) }
    var showPlaylist by remember { mutableStateOf(false) }
    
    // 创建ExoPlayer实例
    val exoPlayer = remember(context) {
        ExoPlayer.Builder(context).build().apply {
            repeatMode = Player.REPEAT_MODE_ONE
        }
    }
    
    // 当前播放状态
    var isPlaying by remember { mutableStateOf(false) }
    
    // 准备播放器
    fun preparePlayer(url: String) {
        exoPlayer.stop()
        exoPlayer.clearMediaItems()
        val mediaItem = MediaItem.fromUri(url)
        exoPlayer.addMediaItem(mediaItem)
        exoPlayer.prepare()
        exoPlayer.playWhenReady = true
        isPlaying = true
    }
    
    // 更新当前频道
    fun updateChannel(channel: PlaylistItem?) {
        currentChannel = channel
        channel?.url?.let { url ->
            preparePlayer(url)
        }
    }
    
    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
        updateChannel(playlistRepository.getCurrentChannel())
    }
    
    // 监听频道变化
    LaunchedEffect(currentChannel) {
        currentChannel?.url?.let { url ->
            preparePlayer(url)
        }
    }
    
    // 清理资源
    DisposableEffect(exoPlayer) {
        onDispose {
            exoPlayer.release()
        }
    }
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.surface)
            .onPreviewKeyEvent { keyEvent ->
                if (keyEvent.type == KeyEventType.KeyDown) {
                    when (keyEvent.key) {
                        Key.DirectionRight -> {
                            if (!showPlaylist) {
                                updateChannel(playlistRepository.getNextChannel())
                                true
                            } else {
                                false
                            }
                        }
                        Key.DirectionLeft -> {
                            if (!showPlaylist) {
                                updateChannel(playlistRepository.getPreviousChannel())
                                true
                            } else {
                                false
                            }
                        }
                        Key.Enter -> {
                            showPlaylist = !showPlaylist
                            true
                        }
                        Key.Back -> {
                            if (showPlaylist) {
                                showPlaylist = false
                                true
                            } else {
                                onExit()
                                true
                            }
                        }
                        else -> false
                    }
                } else {
                    false
                }
            }
            .focusRequester(focusRequester)
            .focusable()
            .onFocusChanged { focusState ->
                isFocused = focusState.isFocused
            }
    ) {
        // 视频播放区域
        Box(
            modifier = Modifier
                .fillMaxSize()
        ) {
            // ExoPlayer视图
            AndroidView(
                factory = { ctx ->
                    StyledPlayerView(ctx).apply {
                        player = exoPlayer
                        useController = false // 禁用默认控制器，使用自定义控制
                        layoutParams = FrameLayout.LayoutParams(
                            ViewGroup.LayoutParams.MATCH_PARENT,
                            ViewGroup.LayoutParams.MATCH_PARENT
                        )
                    }
                },
                modifier = Modifier.fillMaxSize()
            )
            
            // 频道信息覆盖层
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
                    .align(Alignment.TopStart)
                    .background(
                        MaterialTheme.colorScheme.surface.copy(alpha = 0.6f)
                    )
                    .padding(8.dp)
            ) {
                Text(
                    text = currentChannel?.name ?: "无频道",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
        }

        ChannelInfo(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
                .align(Alignment.BottomStart),
            playlistRepository = playlistRepository,
            isFocused = isFocused
        )

        // 播放列表覆盖层
        if (showPlaylist) {
            PlaylistOverlay(
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(16.dp),
                playlistRepository = playlistRepository,
                onChannelSelected = { channel ->
                    updateChannel(channel)
                    showPlaylist = false
                },
                onDismiss = { showPlaylist = false }
            )
        }
    }
}

@Composable
fun ChannelInfo(
    modifier: Modifier = Modifier,
    playlistRepository: PlaylistRepository,
    isFocused: Boolean
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = "频道: ${playlistRepository.getCurrentIndex() + 1}/${playlistRepository.getChannelCount()}",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface
        )

        if (isFocused) {
            Text(
                text = "遥控器控制: ← → 切换频道, 确认键显示列表",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
        }
    }
}

@Composable
fun PlaylistOverlay(
    modifier: Modifier = Modifier,
    playlistRepository: PlaylistRepository,
    onChannelSelected: (PlaylistItem) -> Unit,
    onDismiss: () -> Unit
) {
    val channels = playlistRepository.getChannels()
    val currentIndex = playlistRepository.getCurrentIndex()
    var selectedIndex by remember { mutableStateOf(currentIndex) }
    val listState = rememberLazyListState(initialFirstVisibleItemIndex = currentIndex)
    val focusRequester = remember { FocusRequester() }

    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }

    Card(
        modifier = modifier
            .width(450.dp)
            .height(350.dp)
            .border(2.dp, MaterialTheme.colorScheme.primary.copy(alpha = 0.5f))
            .focusRequester(focusRequester)
            .focusable()
            .onPreviewKeyEvent { keyEvent ->
                if (keyEvent.type == KeyEventType.KeyDown) {
                    when (keyEvent.key) {
                        Key.DirectionUp -> {
                            if (selectedIndex > 0) {
                                selectedIndex--
                            }
                            true
                        }
                        Key.DirectionDown -> {
                            if (selectedIndex < channels.size - 1) {
                                selectedIndex++
                            }
                            true
                        }
                        Key.Enter -> {
                            channels.getOrNull(selectedIndex)?.let { channel ->
                                playlistRepository.jumpToChannel(selectedIndex)
                                onChannelSelected(channel)
                            }
                            true
                        }
                        Key.Back -> {
                            onDismiss()
                            true
                        }
                        else -> false
                    }
                } else {
                    false
                }
            },
        elevation = CardDefaults.cardElevation(defaultElevation = 12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.98f)
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "频道列表",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            LazyColumn(
                state = listState,
                contentPadding = PaddingValues(vertical = 4.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                itemsIndexed(channels) { index, channel ->
                    PlaylistOverlayItem(
                        channel = channel,
                        isSelected = index == selectedIndex,
                        isCurrent = index == currentIndex,
                        onClick = {
                            selectedIndex = index
                            playlistRepository.jumpToChannel(index)
                            onChannelSelected(channel)
                        }
                    )
                }
            }

            Text(
                text = "↑↓ 选择, 确认键播放, 返回键关闭",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                modifier = Modifier.padding(top = 8.dp)
            )
        }
    }
}

@Composable
fun PlaylistOverlayItem(
    channel: PlaylistItem,
    isSelected: Boolean,
    isCurrent: Boolean,
    onClick: () -> Unit
) {
    val backgroundColor = when {
        isCurrent -> MaterialTheme.colorScheme.primary.copy(alpha = 0.4f)
        isSelected -> MaterialTheme.colorScheme.secondary.copy(alpha = 0.3f)
        else -> MaterialTheme.colorScheme.surface.copy(alpha = 0.1f)
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .background(backgroundColor),
        onClick = onClick,
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isSelected) 6.dp else 2.dp
        ),
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = channel.name,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = if (isCurrent) FontWeight.Bold else FontWeight.Normal,
                    color = if (isCurrent) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface
                )

                if (channel.group != null) {
                    Text(
                        text = channel.group,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
            }

            if (isCurrent) {
                Text(
                    text = "●",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}