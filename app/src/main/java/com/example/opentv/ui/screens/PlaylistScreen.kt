package com.example.opentv.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.focusGroup
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.KeyEventType
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.onPreviewKeyEvent
import androidx.compose.ui.input.key.type
import androidx.compose.ui.unit.dp
import com.example.opentv.model.PlaylistItem
import com.example.opentv.repository.PlaylistRepository

@Composable
fun PlaylistScreen(
    playlistRepository: PlaylistRepository,
    onPlayChannel: (PlaylistItem) -> Unit,
    onExit: () -> Unit
) {
    val focusRequester = remember { FocusRequester() }
    val channels = playlistRepository.getChannels()
    var selectedIndex by remember { mutableStateOf(0) }
    var isFocused by remember { mutableStateOf(false) }
    
    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.surface)
            .onPreviewKeyEvent { keyEvent ->
                if (keyEvent.type == KeyEventType.KeyDown) {
                    when (keyEvent.key) {
                        Key.Back -> {
                            onExit()
                            true
                        }
                        else -> false
                    }
                } else {
                    false
                }
            }
            .focusRequester(focusRequester)
            .focusGroup()
            .onFocusChanged { focusState ->
                isFocused = focusState.isFocused
            }
    ) {
        if (channels.isEmpty()) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "未找到频道",
                    style = MaterialTheme.typography.headlineMedium
                )
                Text(
                    text = "请添加M3U播放列表文件",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(top = 8.dp)
                )
            }
        } else {
            LazyColumn(
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                itemsIndexed(channels) { index, channel ->
                    ChannelItem(
                        channel = channel,
                        isSelected = index == selectedIndex,
                        onSelect = {
                            selectedIndex = index
                        },
                        onPlay = {
                            playlistRepository.jumpToChannel(index)
                            onPlayChannel(channel)
                        }
                    )
                }
            }
        }
        
        if (isFocused && channels.isNotEmpty()) {
            Text(
                text = "遥控器控制: ↑ ↓ 选择频道, 确认键播放",
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(16.dp),
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
        }
    }
}

@Composable
fun ChannelItem(
    channel: PlaylistItem,
    isSelected: Boolean,
    onSelect: () -> Unit,
    onPlay: () -> Unit
) {
    val backgroundColor = if (isSelected) {
        MaterialTheme.colorScheme.primary.copy(alpha = 0.3f)
    } else {
        MaterialTheme.colorScheme.surface
    }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .background(backgroundColor)
            .onPreviewKeyEvent { keyEvent ->
                if (keyEvent.type == KeyEventType.KeyDown) {
                    when (keyEvent.key) {
                        Key.DirectionUp -> {
                            // 向上导航由LazyColumn处理
                            false
                        }
                        Key.DirectionDown -> {
                            // 向下导航由LazyColumn处理
                            false
                        }
                        Key.Enter -> {
                            onPlay()
                            true
                        }
                        else -> false
                    }
                } else {
                    false
                }
            },
        onClick = {
            onSelect()
            onPlay()
        },
        elevation = CardDefaults.cardElevation(defaultElevation = if (isSelected) 8.dp else 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = channel.name,
                style = MaterialTheme.typography.titleLarge
            )
            
            if (channel.group != null) {
                Text(
                    text = channel.group,
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
        }
    }
}