package com.example.opentv.ui.screens

import android.widget.Toast
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import java.net.URL

@Composable
fun SettingsDialog(
    showDialog: <PERSON><PERSON><PERSON>,
    onDismiss: () -> Unit,
    onServerAddressSaved: (String) -> Unit
) {
    if (!showDialog) return
    val context = LocalContext.current
    var serverAddress by remember { mutableStateOf("") }

    Dialog(onDismissRequest = onDismiss) {
        Surface(shape = MaterialTheme.shapes.medium) {
            Column(modifier = Modifier.padding(24.dp)) {
                Text(text = "设置远端服务器地址", style = MaterialTheme.typography.titleMedium)
                Spacer(modifier = Modifier.height(16.dp))
                OutlinedTextField(
                    value = serverAddress,
                    onValueChange = { serverAddress = it },
                    label = { Text("服务器地址") },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth()
                )
                Spacer(modifier = Modifier.height(24.dp))
                Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.End) {
                    TextButton(onClick = onDismiss) { Text("取消") }
                    Spacer(modifier = Modifier.width(8.dp))
                    Button(
                        onClick = {
                            if (serverAddress.isNotBlank()) {
                                // 验证 URL 格式是否正确
                                try {
                                    URL(serverAddress)
                                    onServerAddressSaved(serverAddress)
                                } catch (e: Exception) {
                                    Toast.makeText(context, "请输入有效的服务器地址", Toast.LENGTH_SHORT).show()
                                }
                            } else {
                                Toast.makeText(context, "服务器地址不能为空", Toast.LENGTH_SHORT).show()
                            }
                        },
                        enabled = serverAddress.isNotBlank()
                    ) {
                        Text("保存")
                    }
                }
            }
        }
    }
}
